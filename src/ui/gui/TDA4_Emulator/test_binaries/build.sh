#!/bin/bash
# Build script for TDA4 Test Binaries on Linux/macOS

echo "Building TDA4 Test Binaries..."

# Create build directory
mkdir -p build
cd build

# Configure with CMake
echo "Configuring with CMake..."
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release ..
if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

# Build all targets
echo "Building all targets..."
make
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo ""
echo "Build completed successfully!"
echo ""
echo "Executables are located in:"
echo "  build/bin/tda4_r5f_core"
echo "  build/bin/tda4_a72_core"
echo "  build/bin/tda4_c7x_core"
echo ""
