#pragma once

#include <iostream>
#include <string>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <vector>
#include <map>
#include <algorithm>
#include <thread>

#ifdef _WIN32
#include <windows.h>
#include <process.h>
#else
#include <unistd.h>
#endif

namespace TDA4TestCore {

/**
 * @brief Core types supported by the TDA4 emulator
 */
enum class CoreType {
    R5F,    // Real-time Processing Unit
    A72,    // ARM Cortex-A72 Application Processor
    C7x     // TI C7x Digital Signal Processor
};

/**
 * @brief Convert core type to string representation
 */
inline std::string CoreTypeToString(CoreType type) {
    switch (type) {
        case CoreType::R5F: return "R5F";
        case CoreType::A72: return "A72";
        case CoreType::C7x: return "C7x";
        default: return "UNKNOWN";
    }
}

/**
 * @brief Convert string to core type
 */
inline CoreType StringToCoreType(const std::string& str) {
    std::string upper = str;
    std::transform(upper.begin(), upper.end(), upper.begin(), ::toupper);

    if (upper == "R5F") return CoreType::R5F;
    if (upper == "A72") return CoreType::A72;
    if (upper == "C7X") return CoreType::C7x;

    throw std::invalid_argument("Unknown core type: " + str);
}

/**
 * @brief Get current timestamp in HH:MM:SS.mmm format
 */
inline std::string GetTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

/**
 * @brief Split string by delimiter
 */
inline std::vector<std::string> Split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;

    while (std::getline(ss, token, delimiter)) {
        if (!token.empty()) {
            tokens.push_back(token);
        }
    }

    return tokens;
}

/**
 * @brief Trim whitespace from string
 */
inline std::string Trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

/**
 * @brief Base class for TDA4 core emulators
 */
class CoreEmulator {
protected:
    CoreType m_coreType;
    bool m_running;
    std::map<std::string, std::string> m_properties;

public:
    explicit CoreEmulator(CoreType type) : m_coreType(type), m_running(false) {
        // Initialize default properties
        m_properties["version"] = "1.0.0";
        m_properties["status"] = "initialized";
        m_properties["pid"] = std::to_string(getpid());
    }

    virtual ~CoreEmulator() = default;

    /**
     * @brief Start the core emulator
     */
    virtual void Start() {
        m_running = true;
        m_properties["status"] = "running";

        OutputMessage("TDA4 " + CoreTypeToString(m_coreType) + " Core Emulator started");
        OutputMessage("Version: " + m_properties["version"]);
        OutputMessage("Process ID: " + m_properties["pid"]);
        OutputMessage("Ready to receive commands...");
    }

    /**
     * @brief Stop the core emulator
     */
    virtual void Stop() {
        m_running = false;
        m_properties["status"] = "stopped";
        OutputMessage("TDA4 " + CoreTypeToString(m_coreType) + " Core Emulator stopped");
    }

    /**
     * @brief Check if emulator is running
     */
    bool IsRunning() const { return m_running; }

    /**
     * @brief Get core type
     */
    CoreType GetCoreType() const { return m_coreType; }

    /**
     * @brief Output a message to stdout
     */
    void OutputMessage(const std::string& message) {
        std::cout << message << std::endl;
        std::cout.flush();
    }

    /**
     * @brief Output an error message to stderr
     */
    void OutputError(const std::string& message) {
        std::cerr << "ERROR: " << message << std::endl;
        std::cerr.flush();
    }

    /**
     * @brief Process a command received from stdin
     */
    virtual bool ProcessCommand(const std::string& command) {
        auto tokens = Split(Trim(command), ' ');
        if (tokens.empty()) return true;

        std::string cmd = tokens[0];
        std::transform(cmd.begin(), cmd.end(), cmd.begin(), ::tolower);

        if (cmd == "quit" || cmd == "exit") {
            OutputMessage("Received shutdown command");
            return false;
        }
        else if (cmd == "status") {
            HandleStatusCommand();
        }
        else if (cmd == "help") {
            HandleHelpCommand();
        }
        else if (cmd == "ipc_send" && tokens.size() >= 3) {
            HandleIpcCommand(tokens);
        }
        else if (cmd == "ping") {
            OutputMessage("pong");
        }
        else {
            OutputMessage("Unknown command: " + command);
            OutputMessage("Type 'help' for available commands");
        }

        return true;
    }

protected:
    /**
     * @brief Handle status command
     */
    virtual void HandleStatusCommand() {
        OutputMessage("=== Core Status ===");
        OutputMessage("Core Type: " + CoreTypeToString(m_coreType));
        OutputMessage("Status: " + m_properties["status"]);
        OutputMessage("Version: " + m_properties["version"]);
        OutputMessage("Process ID: " + m_properties["pid"]);
        OutputMessage("Running: " + std::string(m_running ? "Yes" : "No"));
    }

    /**
     * @brief Handle help command
     */
    virtual void HandleHelpCommand() {
        OutputMessage("=== Available Commands ===");
        OutputMessage("status          - Show core status information");
        OutputMessage("help            - Show this help message");
        OutputMessage("ping            - Test connectivity (responds with 'pong')");
        OutputMessage("ipc_send <core> <message> - Send IPC message to another core");
        OutputMessage("quit/exit       - Shutdown the core emulator");
    }

    /**
     * @brief Handle IPC command
     */
    virtual void HandleIpcCommand(const std::vector<std::string>& tokens) {
        if (tokens.size() < 3) {
            OutputError("IPC command requires target core and message");
            return;
        }

        std::string targetCore = tokens[1];
        std::string message;
        for (size_t i = 2; i < tokens.size(); ++i) {
            if (i > 2) message += " ";
            message += tokens[i];
        }

        // Simulate IPC message processing
        OutputMessage("IPC message sent to " + targetCore + ": " + message);

        // Simulate some processing delay
        std::this_thread::sleep_for(std::chrono::milliseconds(10));

        OutputMessage("IPC acknowledgment received from " + targetCore);
    }

private:
    // Platform-specific process ID function
    int getpid() {
        #ifdef _WIN32
        return static_cast<int>(GetCurrentProcessId());
        #else
        return ::getpid();
        #endif
    }
};

} // namespace TDA4TestCore
