#include "common.h"
#include <signal.h>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#else
#include <unistd.h>
#endif

using namespace TDA4TestCore;

/**
 * @brief R5F Core Emulator - Real-time Processing Unit
 * 
 * The R5F core is responsible for real-time processing tasks in the TDA4 SoC.
 * This emulator simulates the basic communication interface and command processing.
 */
class R5FCoreEmulator : public CoreEmulator {
private:
    int m_taskCounter;
    bool m_realTimeMode;

public:
    R5FCoreEmulator() : CoreEmulator(CoreType::R5F), m_taskCounter(0), m_realTimeMode(true) {
        m_properties["core_frequency"] = "800MHz";
        m_properties["memory_size"] = "512KB";
        m_properties["real_time_mode"] = "enabled";
    }

    void Start() override {
        CoreEmulator::Start();
        OutputMessage("R5F Core Features:");
        OutputMessage("  - Real-time task processing");
        OutputMessage("  - Low-latency interrupt handling");
        OutputMessage("  - Safety-critical operations");
        OutputMessage("  - Core frequency: " + m_properties["core_frequency"]);
        OutputMessage("  - Available memory: " + m_properties["memory_size"]);
    }

    bool ProcessCommand(const std::string& command) override {
        auto tokens = Split(Trim(command), ' ');
        if (tokens.empty()) return true;

        std::string cmd = tokens[0];
        std::transform(cmd.begin(), cmd.end(), cmd.begin(), ::tolower);

        // Handle R5F-specific commands
        if (cmd == "rt_task" && tokens.size() >= 2) {
            HandleRealTimeTask(tokens);
        }
        else if (cmd == "interrupt" && tokens.size() >= 2) {
            HandleInterrupt(tokens);
        }
        else if (cmd == "safety_check") {
            HandleSafetyCheck();
        }
        else if (cmd == "set_frequency" && tokens.size() >= 2) {
            HandleSetFrequency(tokens[1]);
        }
        else {
            // Fall back to base class command handling
            return CoreEmulator::ProcessCommand(command);
        }

        return true;
    }

protected:
    void HandleStatusCommand() override {
        CoreEmulator::HandleStatusCommand();
        OutputMessage("=== R5F Specific Status ===");
        OutputMessage("Task Counter: " + std::to_string(m_taskCounter));
        OutputMessage("Real-time Mode: " + std::string(m_realTimeMode ? "Enabled" : "Disabled"));
        OutputMessage("Core Frequency: " + m_properties["core_frequency"]);
        OutputMessage("Memory Size: " + m_properties["memory_size"]);
    }

    void HandleHelpCommand() override {
        CoreEmulator::HandleHelpCommand();
        OutputMessage("=== R5F Specific Commands ===");
        OutputMessage("rt_task <name>     - Execute a real-time task");
        OutputMessage("interrupt <id>     - Simulate interrupt handling");
        OutputMessage("safety_check       - Perform safety system check");
        OutputMessage("set_frequency <freq> - Set core operating frequency");
    }

private:
    void HandleRealTimeTask(const std::vector<std::string>& tokens) {
        std::string taskName = tokens[1];
        m_taskCounter++;
        
        OutputMessage("Executing real-time task: " + taskName);
        OutputMessage("Task ID: " + std::to_string(m_taskCounter));
        
        // Simulate task execution time
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        
        OutputMessage("Real-time task '" + taskName + "' completed successfully");
        OutputMessage("Task execution time: 5ms (simulated)");
    }

    void HandleInterrupt(const std::vector<std::string>& tokens) {
        std::string interruptId = tokens[1];
        
        OutputMessage("Interrupt received: IRQ_" + interruptId);
        OutputMessage("Interrupt priority: HIGH");
        OutputMessage("Context switch initiated...");
        
        // Simulate interrupt handling
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
        
        OutputMessage("Interrupt IRQ_" + interruptId + " handled");
        OutputMessage("Context restored");
    }

    void HandleSafetyCheck() {
        OutputMessage("Performing R5F safety system check...");
        
        // Simulate various safety checks
        OutputMessage("  ✓ Memory integrity check passed");
        OutputMessage("  ✓ Watchdog timer operational");
        OutputMessage("  ✓ Error correction codes verified");
        OutputMessage("  ✓ Lockstep mode synchronized");
        OutputMessage("  ✓ Temperature within safe limits");
        
        OutputMessage("Safety check completed - All systems nominal");
    }

    void HandleSetFrequency(const std::string& frequency) {
        m_properties["core_frequency"] = frequency;
        OutputMessage("Core frequency set to: " + frequency);
        OutputMessage("PLL reconfiguration completed");
        OutputMessage("Performance profile updated");
    }
};

// Global emulator instance for signal handling
R5FCoreEmulator* g_emulator = nullptr;

// Signal handler for graceful shutdown
void SignalHandler(int signal) {
    if (g_emulator) {
        g_emulator->OutputMessage("Received signal " + std::to_string(signal) + " - shutting down gracefully");
        g_emulator->Stop();
    }
    exit(0);
}

int main() {
    // Set up signal handling
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);

#ifdef _WIN32
    // Set console to handle UTF-8 on Windows
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    // Create and start the R5F emulator
    R5FCoreEmulator emulator;
    g_emulator = &emulator;
    
    emulator.Start();

    // Main command processing loop
    std::string line;
    while (emulator.IsRunning() && std::getline(std::cin, line)) {
        if (!emulator.ProcessCommand(line)) {
            break;
        }
    }

    emulator.Stop();
    return 0;
}
