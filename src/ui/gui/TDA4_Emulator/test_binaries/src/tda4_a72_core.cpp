#include "common.h"
#include <signal.h>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#else
#include <unistd.h>
#endif

using namespace TDA4TestCore;

/**
 * @brief A72 Core Emulator - ARM Cortex-A72 Application Processor
 * 
 * The A72 core handles high-level application processing, operating system tasks,
 * and complex computational workloads in the TDA4 SoC.
 */
class A72CoreEmulator : public CoreEmulator {
private:
    int m_threadCount;
    bool m_osRunning;
    std::vector<std::string> m_runningProcesses;

public:
    A72CoreEmulator() : CoreEmulator(CoreType::A72), m_threadCount(4), m_osRunning(true) {
        m_properties["core_frequency"] = "2.0GHz";
        m_properties["cache_size"] = "1MB L2";
        m_properties["architecture"] = "ARMv8-A";
        m_properties["thread_count"] = std::to_string(m_threadCount);
        
        // Initialize some default processes
        m_runningProcesses = {"kernel", "systemd", "vision_app", "network_mgr"};
    }

    void Start() override {
        CoreEmulator::Start();
        OutputMessage("A72 Core Features:");
        OutputMessage("  - High-performance application processing");
        OutputMessage("  - Multi-threaded execution (" + std::to_string(m_threadCount) + " threads)");
        OutputMessage("  - Operating system support");
        OutputMessage("  - Core frequency: " + m_properties["core_frequency"]);
        OutputMessage("  - Cache size: " + m_properties["cache_size"]);
        OutputMessage("  - Architecture: " + m_properties["architecture"]);
        OutputMessage("Operating System: Linux (simulated)");
    }

    bool ProcessCommand(const std::string& command) override {
        auto tokens = Split(Trim(command), ' ');
        if (tokens.empty()) return true;

        std::string cmd = tokens[0];
        std::transform(cmd.begin(), cmd.end(), cmd.begin(), ::tolower);

        // Handle A72-specific commands
        if (cmd == "launch_app" && tokens.size() >= 2) {
            HandleLaunchApp(tokens);
        }
        else if (cmd == "kill_process" && tokens.size() >= 2) {
            HandleKillProcess(tokens[1]);
        }
        else if (cmd == "list_processes") {
            HandleListProcesses();
        }
        else if (cmd == "cpu_load") {
            HandleCpuLoad();
        }
        else if (cmd == "memory_info") {
            HandleMemoryInfo();
        }
        else if (cmd == "set_governor" && tokens.size() >= 2) {
            HandleSetGovernor(tokens[1]);
        }
        else {
            // Fall back to base class command handling
            return CoreEmulator::ProcessCommand(command);
        }

        return true;
    }

protected:
    void HandleStatusCommand() override {
        CoreEmulator::HandleStatusCommand();
        OutputMessage("=== A72 Specific Status ===");
        OutputMessage("OS Running: " + std::string(m_osRunning ? "Yes" : "No"));
        OutputMessage("Active Threads: " + std::to_string(m_threadCount));
        OutputMessage("Running Processes: " + std::to_string(m_runningProcesses.size()));
        OutputMessage("Core Frequency: " + m_properties["core_frequency"]);
        OutputMessage("Architecture: " + m_properties["architecture"]);
    }

    void HandleHelpCommand() override {
        CoreEmulator::HandleHelpCommand();
        OutputMessage("=== A72 Specific Commands ===");
        OutputMessage("launch_app <name>  - Launch an application");
        OutputMessage("kill_process <name> - Terminate a process");
        OutputMessage("list_processes     - List all running processes");
        OutputMessage("cpu_load          - Show current CPU utilization");
        OutputMessage("memory_info       - Display memory usage information");
        OutputMessage("set_governor <mode> - Set CPU frequency governor");
    }

private:
    void HandleLaunchApp(const std::vector<std::string>& tokens) {
        std::string appName = tokens[1];
        
        // Check if process already running
        auto it = std::find(m_runningProcesses.begin(), m_runningProcesses.end(), appName);
        if (it != m_runningProcesses.end()) {
            OutputMessage("Process '" + appName + "' is already running");
            return;
        }
        
        OutputMessage("Launching application: " + appName);
        OutputMessage("Allocating memory...");
        OutputMessage("Loading executable...");
        OutputMessage("Creating process context...");
        
        // Simulate app launch time
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        m_runningProcesses.push_back(appName);
        OutputMessage("Application '" + appName + "' launched successfully");
        OutputMessage("Process ID: " + std::to_string(1000 + m_runningProcesses.size()));
    }

    void HandleKillProcess(const std::string& processName) {
        auto it = std::find(m_runningProcesses.begin(), m_runningProcesses.end(), processName);
        if (it == m_runningProcesses.end()) {
            OutputMessage("Process '" + processName + "' not found");
            return;
        }
        
        OutputMessage("Terminating process: " + processName);
        OutputMessage("Sending SIGTERM...");
        
        // Simulate process termination
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        
        m_runningProcesses.erase(it);
        OutputMessage("Process '" + processName + "' terminated successfully");
    }

    void HandleListProcesses() {
        OutputMessage("=== Running Processes ===");
        OutputMessage("PID\tNAME\t\tSTATUS");
        OutputMessage("---\t----\t\t------");
        
        for (size_t i = 0; i < m_runningProcesses.size(); ++i) {
            std::string pid = std::to_string(1000 + i + 1);
            OutputMessage(pid + "\t" + m_runningProcesses[i] + "\t\tRunning");
        }
        
        OutputMessage("Total processes: " + std::to_string(m_runningProcesses.size()));
    }

    void HandleCpuLoad() {
        // Simulate CPU load calculation
        int load1 = rand() % 100;
        int load5 = rand() % 100;
        int load15 = rand() % 100;
        
        OutputMessage("=== CPU Load Information ===");
        OutputMessage("1-minute load:  " + std::to_string(load1) + "%");
        OutputMessage("5-minute load:  " + std::to_string(load5) + "%");
        OutputMessage("15-minute load: " + std::to_string(load15) + "%");
        OutputMessage("Active cores: " + std::to_string(m_threadCount));
        OutputMessage("Current frequency: " + m_properties["core_frequency"]);
    }

    void HandleMemoryInfo() {
        // Simulate memory usage
        int totalMem = 4096;  // 4GB
        int usedMem = 1024 + (rand() % 2048);  // Random usage
        int freeMem = totalMem - usedMem;
        
        OutputMessage("=== Memory Information ===");
        OutputMessage("Total Memory: " + std::to_string(totalMem) + " MB");
        OutputMessage("Used Memory:  " + std::to_string(usedMem) + " MB");
        OutputMessage("Free Memory:  " + std::to_string(freeMem) + " MB");
        OutputMessage("Cache Size:   " + m_properties["cache_size"]);
        OutputMessage("Memory Usage: " + std::to_string((usedMem * 100) / totalMem) + "%");
    }

    void HandleSetGovernor(const std::string& governor) {
        OutputMessage("Setting CPU frequency governor to: " + governor);
        
        if (governor == "performance") {
            m_properties["core_frequency"] = "2.0GHz";
            OutputMessage("Performance mode enabled - maximum frequency");
        }
        else if (governor == "powersave") {
            m_properties["core_frequency"] = "800MHz";
            OutputMessage("Power save mode enabled - minimum frequency");
        }
        else if (governor == "ondemand") {
            m_properties["core_frequency"] = "1.4GHz";
            OutputMessage("On-demand mode enabled - dynamic frequency scaling");
        }
        else {
            OutputMessage("Unknown governor: " + governor);
            OutputMessage("Available governors: performance, powersave, ondemand");
            return;
        }
        
        OutputMessage("CPU governor updated successfully");
    }
};

// Global emulator instance for signal handling
A72CoreEmulator* g_emulator = nullptr;

// Signal handler for graceful shutdown
void SignalHandler(int signal) {
    if (g_emulator) {
        g_emulator->OutputMessage("Received signal " + std::to_string(signal) + " - shutting down gracefully");
        g_emulator->Stop();
    }
    exit(0);
}

int main() {
    // Set up signal handling
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);

#ifdef _WIN32
    // Set console to handle UTF-8 on Windows
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
#endif

    // Create and start the A72 emulator
    A72CoreEmulator emulator;
    g_emulator = &emulator;
    
    emulator.Start();

    // Main command processing loop
    std::string line;
    while (emulator.IsRunning() && std::getline(std::cin, line)) {
        if (!emulator.ProcessCommand(line)) {
            break;
        }
    }

    emulator.Stop();
    return 0;
}
