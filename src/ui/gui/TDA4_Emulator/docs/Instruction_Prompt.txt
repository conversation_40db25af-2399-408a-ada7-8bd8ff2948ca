**TDA4 Emulator Control Panel - Application Design Specification**

A comprehensive cross-platform desktop application built with **Avalonia UI 11.x** and **ReactiveUI** using **C# .NET 9.0** and **MVVM pattern** to control TDA4 Emulator processes. The application interfaces directly with **C++ binary executables (R5F, A72, C7x cores)** via **standard input/output redirection** for local process management.

## **1. UI Architecture & Layout**

### **Header Section**:
- **Company Logo**: Real logo image (logo_text.jpg) positioned in top-left corner (150x50px)
- **Application Title**: "TDA4 Emulator Control Panel" with professional styling
- **Help Button**: Opens comprehensive help dialog with detailed documentation
- **Application Icon**: Custom PNG icon (logo_icon.png) for window and taskbar

### **Main Layout** (Three-Column Design):
- **Left Panel** (350px): Configuration and control panel
- **Center Splitter** (12px): Resizable grid splitter
- **Right Panel** (Expandable): Dynamic test mode interface and terminal area

### **Configuration Panel** (Left Side):
- **Binary Paths Section**:
  - R5F Core Binary: TextBox with Browse button and real-time validation
  - A72 Core Binary: TextBox with Browse button and real-time validation
  - C7x Core Binary: TextBox with Browse button and real-time validation
  - Native file dialogs with platform-specific executable filters (.exe, .bat, .cmd on Windows)
  - Error indicators for invalid paths with descriptive messages

- **Test Mode Selection**:
  - ComboBox with options: IPC, TestMode1, TestMode2
  - Dynamic interface switching based on selected mode
  - Default selection: IPC mode

- **Emulation Control**:
  - "Start Emulation" button (green-themed, bordeaux #800020)
  - "Stop Emulation" button (red-themed, bordeaux #800020)
  - Real-time status indicators and validation

### **Dynamic Test Mode Interface** (Top-Right):
- **IPC Mode**: Inter-Processor Communication testing interface
  - Source core selection with checkboxes (R5F, A72, C7x, All)
  - Destination core selection with checkboxes (R5F, A72, C7x, All)
  - Multi-line command/message input TextBox
  - "Send message" button for direct command transmission
  - Mutual exclusivity: "All" vs individual core selection

- **TestMode1 & TestMode2**: Placeholder interfaces for future functionality
  - Professional placeholder layouts with disabled controls
  - Ready for future implementation of specialized testing scenarios

### **Terminal Output Area** (Bottom-Right):
- **Three-Column Horizontal Layout**:
  - **R5F Core Terminal** (Left): Real-time processing unit output
  - **A72 Core Terminal** (Center): Application processor output
  - **C7x Core Terminal** (Right): Digital signal processor output
- **Terminal Styling**:
  - Always-black background (#000000) with transparent cursor
  - Bright green text (#00FF00) for standard output
  - Color-coded messages: Red (errors), Yellow (system), Cyan (IPC)
  - Monospace font (Consolas, Monaco, monospace)
  - Individual "Clear" buttons for each terminal
  - Auto-scroll to latest output with copy-to-clipboard support

### **Status Bar** (Bottom):
- Current operation status messages
- Emulation running indicator with color-coded status dot
- Real-time feedback for user actions

## **2. Technical Architecture**

### **MVVM Pattern with ReactiveUI**:
- **Models**:
  - `CoreType` enum: R5F, A72, C7x, All
  - `TestMode` enum: IPC, TestMode1, TestMode2
  - `TerminalLine`: Timestamped output with color coding
  - `CoreController`: Individual process lifecycle management

- **ViewModels**:
  - `MainWindowViewModel`: Central application logic with ReactiveUI commands
  - Property validation with real-time feedback
  - Thread-safe UI updates via `Dispatcher.UIThread.InvokeAsync`
  - Comprehensive error handling and logging

- **Views**:
  - `MainWindow.axaml`: Primary application interface
  - `IpcTestModeView.axaml`: IPC testing interface
  - `TestMode1View.axaml` & `TestMode2View.axaml`: Future functionality placeholders
  - `HelpDialog.axaml`: Comprehensive user documentation

- **Services**:
  - `ProcessManager`: Multi-core process orchestration
  - `LoggingService`: Centralized file-based logging
  - Cross-platform file dialog integration

### **Process Management**:
- **Direct Process Communication**: Raw command transmission via stdin
- **Real-time Output Capture**: stdout/stderr redirection with live streaming
- **Graceful Lifecycle Management**: Proper startup, monitoring, and shutdown
- **Error Recovery**: Process crash detection and restart capabilities

## **3. Communication Protocol**

### **Local Process IO Redirection**:
- **No Network Dependencies**: Direct stdin/stdout/stderr communication
- **Raw Command Transmission**: No intermediate formatting or protocols
- **Real-time Streaming**: Immediate output capture and display
- **Bidirectional Communication**: Command sending and response monitoring

### **IPC Message Format**:
- Direct text transmission to process stdin
- Timestamped logging with core identification
- Support for multi-core broadcast operations
- Command history and replay capabilities

## **4. Advanced Features**

### **Cross-Platform Compatibility**:
- **.NET 9.0 Runtime**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Platform-Specific Handling**: Executable detection, file paths, fonts
- **Responsive Design**: Minimum 1024x768, default 1920x1080 (Full HD)
- **Native Look & Feel**: Platform-appropriate dialogs and styling

### **User Experience Enhancements**:
- **Professional Branding**: Custom logo and icon integration
- **Consistent Terminology**: "Emulation" throughout (not "Simulation")
- **Intuitive Controls**: Logical grouping and workflow-based layout
- **Comprehensive Help**: Built-in documentation with troubleshooting guide
- **Keyboard Shortcuts**: F1 (Help), Ctrl+S (Start), Ctrl+T (Stop)

### **Quality & Reliability**:
- **Input Validation**: Real-time path validation with descriptive errors
- **Thread Safety**: All UI updates properly dispatched to main thread
- **Memory Management**: Proper disposal patterns and resource cleanup
- **Error Handling**: Comprehensive exception handling with user feedback
- **Logging System**: Detailed file-based logging for debugging and audit

## **5. File Structure & Organization**

```
TDA4_Emulator/
├── Models/
│   ├── CoreType.cs              # Core type enumeration and extensions
│   ├── TestMode.cs              # Test mode enumeration and extensions
│   ├── TerminalLine.cs          # Terminal output data model
│   └── CoreController.cs        # Individual core process management
├── ViewModels/
│   ├── ViewModelBase.cs         # Base class with IDisposable
│   └── MainWindowViewModel.cs   # Main application logic
├── Views/
│   ├── MainWindow.axaml         # Primary application interface
│   ├── HelpDialog.axaml         # Comprehensive help documentation
│   └── TestModes/               # Dynamic test mode interfaces
│       ├── IpcTestModeView.axaml
│       ├── TestMode1View.axaml
│       └── TestMode2View.axaml
├── Services/
│   ├── ProcessManager.cs        # Multi-core process orchestration
│   └── LoggingService.cs        # Centralized logging service
├── Converters/
│   ├── CoreTypeToDisplayNameConverter.cs
│   ├── BoolToColorConverter.cs
│   ├── TerminalLinesToTextConverter.cs
│   └── TestModeToViewConverter.cs
├── Assets/
│   ├── logo_text.jpg           # Header logo image
│   └── logo_icon.png           # Application icon
└── test_binaries/              # Mock executables for testing
    ├── r5f_core_mock.bat
    ├── a72_core_mock.bat
    └── c7x_core_mock.bat
```

## **6. Deployment & Distribution**

### **Build Configuration**:
- **Self-Contained Deployment**: Includes .NET runtime for target platforms
- **Platform-Specific Packages**: Windows (exe), macOS (app), Linux (AppImage)
- **Asset Embedding**: Logo and icon files included in application bundle
- **Test Binaries**: Mock executables for demonstration and testing

### **System Requirements**:
- **Minimum RAM**: 4GB
- **Disk Space**: 100MB for application + space for core binaries
- **Display**: 1024x768 minimum, 1920x1080 recommended
- **Permissions**: Execute permissions for core binary files

## **7. Key Differentiators**

### **Professional Design**:
- Custom branding with real logo and icon
- Bordeaux (#800020) and light gray (#F5F5F5) color scheme
- Professional typography and spacing
- Consistent visual hierarchy

### **Advanced Terminal Experience**:
- Always-black terminal backgrounds with proper focus handling
- Color-coded output for different message types
- Individual terminal columns for better monitoring
- Copy-to-clipboard and clear functionality

### **Robust Process Management**:
- Graceful process startup and shutdown
- Real-time health monitoring
- Automatic error recovery
- Comprehensive logging and debugging support

### **Extensible Architecture**:
- Plugin-ready test mode system
- Easy addition of new core types
- Modular component design
- Future-proof interface patterns

This specification represents the current state of the TDA4 Emulator Control Panel, incorporating all implemented features, improvements, and architectural decisions made during development.

