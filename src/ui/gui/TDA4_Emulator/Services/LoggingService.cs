using System;
using System.IO;
using System.Threading.Tasks;

namespace TDA4_Emulator.Services;

/// <summary>
/// Provides centralized logging functionality for the application
/// </summary>
public class LoggingService
{
    private static readonly Lazy<LoggingService> _instance = new(() => new LoggingService());
    private readonly string _logFilePath;
    private readonly object _lockObject = new();
    
    public static LoggingService Instance => _instance.Value;
    
    private LoggingService()
    {
        var logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
        Directory.CreateDirectory(logsDirectory);
        
        var fileName = $"tda4_emulator_{DateTime.Now:yyyyMMdd}.log";
        _logFilePath = Path.Combine(logsDirectory, fileName);
    }
    
    /// <summary>
    /// Logs an information message
    /// </summary>
    public void LogInfo(string message)
    {
        LogMessage("INFO", message);
    }
    
    /// <summary>
    /// Logs a warning message
    /// </summary>
    public void LogWarning(string message)
    {
        LogMessage("WARN", message);
    }
    
    /// <summary>
    /// Logs an error message
    /// </summary>
    public void LogError(string message)
    {
        LogMessage("ERROR", message);
    }
    
    /// <summary>
    /// Logs an error with exception details
    /// </summary>
    public void LogError(string message, Exception exception)
    {
        LogMessage("ERROR", $"{message}: {exception}");
    }
    
    /// <summary>
    /// Logs a debug message (only in debug builds)
    /// </summary>
    public void LogDebug(string message)
    {
#if DEBUG
        LogMessage("DEBUG", message);
#endif
    }
    
    private void LogMessage(string level, string message)
    {
        var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{level}] {message}";
        
        lock (_lockObject)
        {
            try
            {
                File.AppendAllText(_logFilePath, logEntry + Environment.NewLine);
            }
            catch
            {
                // Ignore logging errors to prevent cascading failures
            }
        }
        
        // Also output to console in debug mode
#if DEBUG
        Console.WriteLine(logEntry);
#endif
    }
    
    /// <summary>
    /// Gets the path to the current log file
    /// </summary>
    public string GetLogFilePath() => _logFilePath;
    
    /// <summary>
    /// Clears the current log file
    /// </summary>
    public void ClearLog()
    {
        lock (_lockObject)
        {
            try
            {
                File.WriteAllText(_logFilePath, string.Empty);
            }
            catch
            {
                // Ignore errors
            }
        }
    }
}
