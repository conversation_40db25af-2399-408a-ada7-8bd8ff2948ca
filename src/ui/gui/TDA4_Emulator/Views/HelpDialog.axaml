<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="TDA4_Emulator.Views.HelpDialog"
        Title="TDA4 Emulator Help"
        Width="600" Height="500"
        WindowStartupLocation="CenterOwner"
        CanResize="False">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="TDA4 Emulator Control Panel - Help"
                   FontSize="18" FontWeight="Bold"
                   Foreground="#800020" Margin="0,0,0,16"/>

        <!-- Content -->
        <ScrollViewer Grid.Row="1">
            <StackPanel Spacing="16">

                <!-- Quick Start Guide -->
                <Border Background="#F0F8FF" BorderBrush="#800020" BorderThickness="1"
                        CornerRadius="4" Padding="12">
                    <StackPanel Spacing="8">
                        <TextBlock FontWeight="Bold" FontSize="16" Text="🚀 Quick Start Guide" Foreground="#800020"/>
                        <TextBlock TextWrapping="Wrap">
                            1. <Bold>Configure Binaries:</Bold> Use Browse buttons to select executable files for each core (R5F, A72, C7x)
                            <LineBreak/>2. <Bold>Select Test Mode:</Bold> Choose IPC, TestMode1, or TestMode2 from the dropdown
                            <LineBreak/>3. <Bold>Start Emulation:</Bold> Click "Start Emulation" to launch configured processes
                            <LineBreak/>4. <Bold>Send Commands:</Bold> Use the test mode interface to interact with cores
                            <LineBreak/>5. <Bold>Monitor Output:</Bold> Watch individual core terminals for real-time feedback
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!-- Binary Configuration -->
                <StackPanel Spacing="8">
                    <TextBlock FontWeight="Bold" FontSize="14" Text="📁 Binary Configuration" Foreground="#800020"/>
                    <TextBlock TextWrapping="Wrap">
                        <Bold>R5F Core:</Bold> Real-time Processing Unit - handles time-critical operations and system control
                        <LineBreak/><Bold>A72 Core:</Bold> ARM Cortex-A72 Application Processor - runs high-level applications and OS
                        <LineBreak/><Bold>C7x Core:</Bold> TI C7x DSP - performs digital signal processing and AI acceleration
                        <LineBreak/><LineBreak/><Bold>File Requirements:</Bold>
                        <LineBreak/>• Windows: .exe, .bat, .cmd files
                        <LineBreak/>• Linux/macOS: Any executable file
                        <LineBreak/>• Files must exist and have proper permissions
                        <LineBreak/>• Use test_binaries/ folder for mock testing
                    </TextBlock>
                </StackPanel>

                <!-- Test Modes -->
                <StackPanel Spacing="8">
                    <TextBlock FontWeight="Bold" FontSize="14" Text="🧪 Test Modes" Foreground="#800020"/>
                    <TextBlock TextWrapping="Wrap">
                        <Bold>IPC Mode:</Bold> Inter-Processor Communication testing
                        <LineBreak/>• Select source and destination cores using checkboxes
                        <LineBreak/>• Enter commands/messages in the text area
                        <LineBreak/>• Commands are sent directly to core processes via stdin
                        <LineBreak/>• No intermediate formatting - raw text transmission
                        <LineBreak/><LineBreak/><Bold>TestMode1 &amp; TestMode2:</Bold> Reserved for future functionality
                        <LineBreak/>• Placeholder interfaces for specialized testing scenarios
                        <LineBreak/>• Will be implemented based on specific requirements
                    </TextBlock>
                </StackPanel>

                <!-- Terminal Output -->
                <StackPanel Spacing="8">
                    <TextBlock FontWeight="Bold" FontSize="14" Text="💻 Terminal Output" Foreground="#800020"/>
                    <TextBlock TextWrapping="Wrap">
                        <Bold>Three-Column Layout:</Bold>
                        <LineBreak/>• R5F Core Terminal (left) - Real-time processing output
                        <LineBreak/>• A72 Core Terminal (center) - Application processor output
                        <LineBreak/>• C7x Core Terminal (right) - DSP processing output
                        <LineBreak/><LineBreak/><Bold>Color Coding:</Bold>
                        <LineBreak/>• <Run Foreground="LimeGreen">Green:</Run> Standard output from processes
                        <LineBreak/>• <Run Foreground="Red">Red:</Run> Error messages and exceptions
                        <LineBreak/>• <Run Foreground="Yellow">Yellow:</Run> System messages from emulator
                        <LineBreak/>• <Run Foreground="Cyan">Cyan:</Run> IPC communication logs
                        <LineBreak/><LineBreak/><Bold>Features:</Bold>
                        <LineBreak/>• Auto-scroll to latest output
                        <LineBreak/>• Individual Clear buttons for each terminal
                        <LineBreak/>• Timestamp and core identification for each line
                        <LineBreak/>• Copy-to-clipboard support (Ctrl+C)
                    </TextBlock>
                </StackPanel>

                <!-- Process Management -->
                <StackPanel Spacing="8">
                    <TextBlock FontWeight="Bold" FontSize="14" Text="⚙️ Process Management" Foreground="#800020"/>
                    <TextBlock TextWrapping="Wrap">
                        <Bold>Lifecycle Control:</Bold>
                        <LineBreak/>• Start Emulation: Launches all configured core processes
                        <LineBreak/>• Stop Emulation: Gracefully terminates all running processes
                        <LineBreak/>• Automatic cleanup on application exit
                        <LineBreak/><LineBreak/><Bold>Communication:</Bold>
                        <LineBreak/>• Direct stdin/stdout redirection to core processes
                        <LineBreak/>• Real-time output capture and display
                        <LineBreak/>• Bidirectional communication support
                        <LineBreak/>• Process health monitoring and status reporting
                    </TextBlock>
                </StackPanel>

                <!-- Keyboard Shortcuts -->
                <StackPanel Spacing="8">
                    <TextBlock FontWeight="Bold" FontSize="14" Text="⌨️ Keyboard Shortcuts" Foreground="#800020"/>
                    <TextBlock TextWrapping="Wrap">
                        <Bold>F1:</Bold> Show this help dialog
                        <LineBreak/><Bold>Ctrl+S:</Bold> Start emulation
                        <LineBreak/><Bold>Ctrl+T:</Bold> Stop emulation
                        <LineBreak/><Bold>Ctrl+C:</Bold> Copy terminal content
                        <LineBreak/><Bold>Ctrl+L:</Bold> Clear current terminal
                        <LineBreak/><Bold>F5:</Bold> Refresh/restart application
                        <LineBreak/><Bold>Alt+F4:</Bold> Exit application
                    </TextBlock>
                </StackPanel>

                <!-- Troubleshooting -->
                <StackPanel Spacing="8">
                    <TextBlock FontWeight="Bold" FontSize="14" Text="🔧 Troubleshooting" Foreground="#800020"/>
                    <TextBlock TextWrapping="Wrap">
                        <Bold>Common Issues:</Bold>
                        <LineBreak/>• <Bold>Process won't start:</Bold> Check binary path and file permissions
                        <LineBreak/>• <Bold>No output visible:</Bold> Verify process is writing to stdout/stderr
                        <LineBreak/>• <Bold>Commands not working:</Bold> Ensure process accepts stdin input
                        <LineBreak/>• <Bold>UI freezing:</Bold> Check for infinite loops in process output
                        <LineBreak/><LineBreak/><Bold>Log Files:</Bold>
                        <LineBreak/>• Location: logs/tda4_emulator_YYYYMMDD.log
                        <LineBreak/>• Contains detailed operation logs and error messages
                        <LineBreak/>• Useful for debugging and support requests
                        <LineBreak/><LineBreak/><Bold>Performance:</Bold>
                        <LineBreak/>• Maximum 1000 lines/second per core supported
                        <LineBreak/>• Use Clear buttons to free memory if needed
                        <LineBreak/>• Application auto-manages terminal buffer size
                    </TextBlock>
                </StackPanel>

                <!-- Version Information -->
                <Border Background="#F5F5F5" BorderBrush="#CCCCCC" BorderThickness="1"
                        CornerRadius="4" Padding="12">
                    <StackPanel Spacing="4">
                        <TextBlock FontWeight="Bold" Text="📋 Version Information"/>
                        <TextBlock TextWrapping="Wrap" FontSize="11">
                            TDA4 Emulator Control Panel v2.0
                            <LineBreak/>Built with Avalonia UI 11.3.0 and ReactiveUI 19.5.41
                            <LineBreak/>Target Framework: .NET 9.0
                            <LineBreak/>Cross-platform support: Windows, macOS, Linux
                            <LineBreak/>© 2024 SIL-TDA4-EMULATOR Project
                        </TextBlock>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Button Grid.Row="2" Content="Close" HorizontalAlignment="Right"
                Padding="20,8" Margin="0,16,0,0"
                Click="CloseButton_Click"/>
    </Grid>

</Window>
