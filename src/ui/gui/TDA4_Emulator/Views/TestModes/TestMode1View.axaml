<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="TDA4_Emulator.Views.TestModes.TestMode1View">

    <Border Background="#F5F5F5" Padding="12">
        <StackPanel Spacing="12" HorizontalAlignment="Center" VerticalAlignment="Center">
            
            <TextBlock Text="TestMode1" FontSize="24" FontWeight="Bold" 
                       Foreground="#800020" HorizontalAlignment="Center"/>
            
            <TextBlock Text="This test mode is reserved for future functionality." 
                       FontSize="14" Foreground="#666666" HorizontalAlignment="Center"/>
            
            <TextBlock Text="Implementation coming soon..." 
                       FontSize="12" FontStyle="Italic" Foreground="#999999" 
                       HorizontalAlignment="Center"/>
            
            <!-- Placeholder controls for future implementation -->
            <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" 
                    CornerRadius="4" Padding="20" Margin="0,20">
                <StackPanel Spacing="8">
                    <Label Content="Test Parameters:" FontWeight="Bold"/>
                    <TextBox Watermark="Parameter 1..." IsEnabled="False"/>
                    <TextBox Watermark="Parameter 2..." IsEnabled="False"/>
                    <Button Content="Execute Test" IsEnabled="False" Classes="primary"/>
                </StackPanel>
            </Border>
            
        </StackPanel>
    </Border>

</UserControl>
