<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:TDA4_Emulator.ViewModels"
        xmlns:models="using:TDA4_Emulator.Models"
        xmlns:converters="using:TDA4_Emulator.Converters"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="1920" d:DesignHeight="1080"
        x:Class="TDA4_Emulator.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Icon="/Assets/logo_icon.png"
        Title="TDA4 Emulator Control Panel"
        Width="1920" Height="1080"
        MinWidth="1024" MinHeight="768"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <converters:CoreTypeToDisplayNameConverter x:Key="CoreTypeToDisplayNameConverter"/>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:TestModeToViewConverter x:Key="TestModeToViewConverter"/>
        <converters:CollectionContainsConverter x:Key="CollectionContainsConverter"/>
    </Window.Resources>

    <Design.DataContext>
        <vm:MainWindowViewModel/>
    </Design.DataContext>

    <Window.Styles>
        <!-- Define custom styles for the bordeaux/gray theme -->
        <Style Selector="Button.primary">
            <Setter Property="Background" Value="#800020"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#600015"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <Style Selector="Button.primary:pointerover">
            <Setter Property="Background" Value="#A00025"/>
        </Style>

        <Style Selector="Button.secondary">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="4"/>
        </Style>

        <Style Selector="Button.secondary:pointerover">
            <Setter Property="Background" Value="#E8E8E8"/>
        </Style>

        <Style Selector="TextBox.terminal">
            <Setter Property="Background" Value="#FFFFFF"/>
            <Setter Property="Foreground" Value="#000000"/>
            <Setter Property="FontFamily" Value="Consolas, Monaco, monospace"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
            <Setter Property="CaretBrush" Value="Transparent"/>
            <Setter Property="SelectionBrush" Value="#E0E0E0"/>
        </Style>

        <Style Selector="TextBox.terminal:focus">
            <Setter Property="Background" Value="#FFFFFF"/>
            <Setter Property="BorderBrush" Value="#800020"/>
        </Style>

        <Style Selector="TextBox.path">
            <Setter Property="Margin" Value="4"/>
            <Setter Property="Padding" Value="8,4"/>
        </Style>

        <Style Selector="TextBox.error">
            <Setter Property="BorderBrush" Value="Red"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>

        <Style Selector="Label.section">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#800020"/>
            <Setter Property="Margin" Value="0,8,0,4"/>
        </Style>

        <!-- Custom checkbox styles with bordeaux highlight and no borders -->
        <Style Selector="CheckBox">
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="CheckBox /template/ Border#NormalRectangle">
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Background" Value="Transparent"/>
        </Style>

        <Style Selector="CheckBox:checked /template/ Border#NormalRectangle">
            <Setter Property="Background" Value="#800020"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="CheckBox:checked:pointerover /template/ Border#NormalRectangle">
            <Setter Property="Background" Value="#A00025"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="CheckBox:pointerover /template/ Border#NormalRectangle">
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>
    </Window.Styles>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Header -->
            <RowDefinition Height="*"/>    <!-- Main Content -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="#800020" Padding="12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo -->
                <Border Grid.Column="0" Background="White" Width="150" Height="50"
                        CornerRadius="4" Margin="0,0,12,0">
                    <Image Source="/Assets/logo_text.jpg"
                           Stretch="Uniform"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Margin="4"/>
                </Border>

                <!-- Title -->
                <TextBlock Grid.Column="1" Text="TDA4 Emulator Control Panel"
                           FontSize="24" FontWeight="Bold" Foreground="White"
                           VerticalAlignment="Center"/>

                <!-- Help Button -->
                <Button Grid.Column="2" Content="Help" Classes="secondary"
                        Command="{Binding ShowHelpCommand}"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="12">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="350"/>  <!-- Configuration Panel -->
                <ColumnDefinition Width="12"/>   <!-- Splitter -->
                <ColumnDefinition Width="*"/>    <!-- Terminal Area -->
            </Grid.ColumnDefinitions>

            <!-- Configuration Panel -->
            <ScrollViewer Grid.Column="0">
                <StackPanel Spacing="8">

                    <!-- Binary Paths Section -->
                    <Label Content="Binary Paths" Classes="section"/>

                    <!-- R5F Binary Path -->
                    <StackPanel>
                        <Label Content="R5F Core Binary:"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" Text="{Binding R5FBinaryPath}"
                                     Classes="path" Watermark="Select R5F binary executable..."/>
                            <TextBlock Grid.Column="1" Text="✓" Foreground="Green" FontWeight="Bold"
                                       VerticalAlignment="Center" Margin="4,0"
                                       IsVisible="{Binding R5FPathValid}"/>
                            <TextBlock Grid.Column="1" Text="✗" Foreground="Red" FontWeight="Bold"
                                       VerticalAlignment="Center" Margin="4,0"
                                       IsVisible="{Binding R5FPathError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                            <Button Grid.Column="2" Content="Browse" Classes="secondary"
                                    Command="{Binding BrowseR5FBinaryCommand}"/>
                        </Grid>
                        <TextBlock Text="{Binding R5FPathError}" Foreground="Red"
                                   FontSize="11" IsVisible="{Binding R5FPathError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                    </StackPanel>

                    <!-- A72 Binary Path -->
                    <StackPanel>
                        <Label Content="A72 Core Binary:"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" Text="{Binding A72BinaryPath}"
                                     Classes="path" Watermark="Select A72 binary executable..."/>
                            <TextBlock Grid.Column="1" Text="✓" Foreground="Green" FontWeight="Bold"
                                       VerticalAlignment="Center" Margin="4,0"
                                       IsVisible="{Binding A72PathValid}"/>
                            <TextBlock Grid.Column="1" Text="✗" Foreground="Red" FontWeight="Bold"
                                       VerticalAlignment="Center" Margin="4,0"
                                       IsVisible="{Binding A72PathError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                            <Button Grid.Column="2" Content="Browse" Classes="secondary"
                                    Command="{Binding BrowseA72BinaryCommand}"/>
                        </Grid>
                        <TextBlock Text="{Binding A72PathError}" Foreground="Red"
                                   FontSize="11" IsVisible="{Binding A72PathError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                    </StackPanel>

                    <!-- C7x Binary Path -->
                    <StackPanel>
                        <Label Content="C7x Core Binary:"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox Grid.Column="0" Text="{Binding C7xBinaryPath}"
                                     Classes="path" Watermark="Select C7x binary executable..."/>
                            <TextBlock Grid.Column="1" Text="✓" Foreground="Green" FontWeight="Bold"
                                       VerticalAlignment="Center" Margin="4,0"
                                       IsVisible="{Binding C7xPathValid}"/>
                            <TextBlock Grid.Column="1" Text="✗" Foreground="Red" FontWeight="Bold"
                                       VerticalAlignment="Center" Margin="4,0"
                                       IsVisible="{Binding C7xPathError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                            <Button Grid.Column="2" Content="Browse" Classes="secondary"
                                    Command="{Binding BrowseC7xBinaryCommand}"/>
                        </Grid>
                        <TextBlock Text="{Binding C7xPathError}" Foreground="Red"
                                   FontSize="11" IsVisible="{Binding C7xPathError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                    </StackPanel>

                    <!-- Test Mode Selection -->
                    <Label Content="Test Mode" Classes="section"/>
                    <ComboBox ItemsSource="{Binding AvailableTestModes}"
                              SelectedItem="{Binding SelectedTestMode}"
                              HorizontalAlignment="Stretch">
                        <ComboBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}"/>
                            </DataTemplate>
                        </ComboBox.ItemTemplate>
                    </ComboBox>

                    <!-- Emulation Control -->
                    <Label Content="Emulation Control" Classes="section"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Button Grid.Column="0" Content="Start Emulation" Classes="primary"
                                Command="{Binding StartEmulationCommand}"/>
                        <Button Grid.Column="1" Content="Stop Emulation" Classes="primary"
                                Command="{Binding StopEmulationCommand}"/>
                    </Grid>

                </StackPanel>
            </ScrollViewer>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Background="#CCCCCC" Width="2"/>

            <!-- Right Side Area -->
            <Grid Grid.Column="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/> <!-- Test Mode Content -->
                    <RowDefinition Height="*"/>    <!-- Terminal Area -->
                </Grid.RowDefinitions>

                <!-- Dynamic Test Mode Content -->
                <ContentControl Grid.Row="0"
                                Content="{Binding SelectedTestMode, Converter={StaticResource TestModeToViewConverter}}"
                                DataContext="{Binding}"/>

                <!-- Terminal Area with Three Columns -->
                <Grid Grid.Row="1" Margin="0,8,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- R5F Core Terminal -->
                    <Border Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="1"
                            CornerRadius="4" Margin="0,0,4,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Label Grid.Row="0" Content="R5F Core Terminal"
                                   Background="#800020" Foreground="White"
                                   Padding="8,4" FontWeight="Bold"/>

                            <TextBox Grid.Row="1" Classes="terminal"
                                     Text="{Binding R5FTerminalOutput}"
                                     Margin="4"/>

                            <Button Grid.Row="2" Content="Clear" Classes="secondary"
                                    Command="{Binding ClearTerminalCommand}"
                                    CommandParameter="{x:Static models:CoreType.R5F}"
                                    HorizontalAlignment="Right" Margin="4"/>
                        </Grid>
                    </Border>

                    <!-- A72 Core Terminal -->
                    <Border Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="1"
                            CornerRadius="4" Margin="4,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Label Grid.Row="0" Content="A72 Core Terminal"
                                   Background="#800020" Foreground="White"
                                   Padding="8,4" FontWeight="Bold"/>

                            <TextBox Grid.Row="1" Classes="terminal"
                                     Text="{Binding A72TerminalOutput}"
                                     Margin="4"/>

                            <Button Grid.Row="2" Content="Clear" Classes="secondary"
                                    Command="{Binding ClearTerminalCommand}"
                                    CommandParameter="{x:Static models:CoreType.A72}"
                                    HorizontalAlignment="Right" Margin="4"/>
                        </Grid>
                    </Border>

                    <!-- C7x Core Terminal -->
                    <Border Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="1"
                            CornerRadius="4" Margin="4,0,0,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Label Grid.Row="0" Content="C7x Core Terminal"
                                   Background="#800020" Foreground="White"
                                   Padding="8,4" FontWeight="Bold"/>

                            <TextBox Grid.Row="1" Classes="terminal"
                                     Text="{Binding C7xTerminalOutput}"
                                     Margin="4"/>

                            <Button Grid.Row="2" Content="Clear" Classes="secondary"
                                    Command="{Binding ClearTerminalCommand}"
                                    CommandParameter="{x:Static models:CoreType.C7x}"
                                    HorizontalAlignment="Right" Margin="4"/>
                        </Grid>
                    </Border>
                </Grid>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#CCCCCC"
                BorderThickness="0,1,0,0" Padding="12,6">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}"
                           VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="12">
                    <TextBlock Text="{Binding IsEmulationRunning, StringFormat='Running: {0}'}"
                               VerticalAlignment="Center"/>
                    <Ellipse Width="12" Height="12"
                             Fill="{Binding IsEmulationRunning, Converter={StaticResource BoolToColorConverter}}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>

</Window>
