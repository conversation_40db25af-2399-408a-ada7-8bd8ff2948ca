using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Text;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using ReactiveUI;
using TDA4_Emulator.Models;

namespace TDA4_Emulator.Controls;

public partial class MultiSelectComboBox : User<PERSON><PERSON><PERSON>, IDisposable
{
    private bool _isDropdownOpen;
    private bool _isUpdatingSelection;

    public static readonly StyledProperty<ObservableCollection<CoreType>> SelectedCoresProperty =
        AvaloniaProperty.Register<MultiSelectComboBox, ObservableCollection<CoreType>>(
            nameof(SelectedCores), new ObservableCollection<CoreType>());

    public static readonly StyledProperty<string> PlaceholderTextProperty =
        AvaloniaProperty.Register<MultiSelectComboBox, string>(
            nameof(PlaceholderText), "Select cores...");

    public ObservableCollection<CoreType> SelectedCores
    {
        get => GetValue(SelectedCoresProperty);
        set => SetValue(SelectedCoresProperty, value);
    }

    public string PlaceholderText
    {
        get => GetValue(PlaceholderTextProperty);
        set => SetValue(PlaceholderTextProperty, value);
    }

    public ObservableCollection<CoreType> IndividualCores { get; }

    public ReactiveCommand<Unit, Unit> ToggleAllCommand { get; }
    public ReactiveCommand<CoreType, Unit> ToggleCoreCommand { get; }

    private bool _isAllSelected;
    public bool IsAllSelected
    {
        get => _isAllSelected;
        set
        {
            if (_isAllSelected != value)
            {
                _isAllSelected = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(AreIndividualCoresEnabled));
                OnPropertyChanged(nameof(DisplayText));
            }
        }
    }

    public bool AreIndividualCoresEnabled => !IsAllSelected;

    public string DisplayText
    {
        get
        {
            if (IsAllSelected)
                return "All Cores";

            if (!SelectedCores.Any())
                return PlaceholderText;

            var displayNames = SelectedCores
                .Where(c => c != CoreType.All)
                .Select(c => c.GetDisplayName())
                .ToList();

            if (!displayNames.Any())
                return PlaceholderText;

            return string.Join(", ", displayNames);
        }
    }

    public MultiSelectComboBox()
    {
        InitializeComponent();

        IndividualCores = new ObservableCollection<CoreType>(CoreTypeExtensions.GetIndividualCores());

        ToggleAllCommand = ReactiveCommand.Create(ToggleAll);
        ToggleCoreCommand = ReactiveCommand.Create<CoreType>(ToggleCore);

        // Subscribe to SelectedCores changes
        SelectedCores.CollectionChanged += (s, e) => UpdateDisplayText();

        DataContext = this;
    }

    private void ToggleAll()
    {
        if (_isUpdatingSelection) return;

        _isUpdatingSelection = true;
        try
        {
            if (IsAllSelected)
            {
                // Unselect All
                IsAllSelected = false;
                SelectedCores.Remove(CoreType.All);
            }
            else
            {
                // Select All and clear individual selections
                IsAllSelected = true;
                SelectedCores.Clear();
                SelectedCores.Add(CoreType.All);
            }
        }
        finally
        {
            _isUpdatingSelection = false;
        }
    }

    private void ToggleCore(CoreType coreType)
    {
        if (_isUpdatingSelection || coreType == CoreType.All) return;

        _isUpdatingSelection = true;
        try
        {
            if (SelectedCores.Contains(coreType))
            {
                SelectedCores.Remove(coreType);
            }
            else
            {
                // Remove "All" if selecting individual cores
                if (IsAllSelected)
                {
                    IsAllSelected = false;
                    SelectedCores.Remove(CoreType.All);
                }

                SelectedCores.Add(coreType);
            }
        }
        finally
        {
            _isUpdatingSelection = false;
        }
    }

    private void UpdateDisplayText()
    {
        if (_isUpdatingSelection) return;

        // Check if "All" is selected
        IsAllSelected = SelectedCores.Contains(CoreType.All);

        OnPropertyChanged(nameof(DisplayText));
    }

    private void HeaderButton_Click(object? sender, RoutedEventArgs e)
    {
        _isDropdownOpen = !_isDropdownOpen;

        if (this.FindControl<Border>("DropdownPanel") is Border panel)
        {
            panel.IsVisible = _isDropdownOpen;
        }
    }

    protected override void OnPointerPressed(Avalonia.Input.PointerPressedEventArgs e)
    {
        // Close dropdown when clicking outside
        if (_isDropdownOpen)
        {
            var dropdownPanel = this.FindControl<Border>("DropdownPanel");
            if (dropdownPanel != null && !dropdownPanel.Bounds.Contains(e.GetPosition(this)))
            {
                _isDropdownOpen = false;
                dropdownPanel.IsVisible = false;
            }
        }

        base.OnPointerPressed(e);
    }

    public new event System.ComponentModel.PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
    }

    public void Dispose()
    {
        // Clean up any subscriptions if needed
    }
}
