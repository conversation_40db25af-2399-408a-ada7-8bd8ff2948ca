using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Linq;
using System.Text;
using Avalonia.Data.Converters;
using TDA4_Emulator.Models;

namespace TDA4_Emulator.Converters;

/// <summary>
/// Converts a collection of TerminalLine objects to formatted text for display
/// </summary>
public class TerminalLinesToTextConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        try
        {
            // Handle null or empty collections
            if (value == null)
            {
                return string.Empty;
            }

            // Handle ObservableCollection<TerminalLine> specifically
            if (value is ObservableCollection<TerminalLine> terminalLines)
            {
                var sb = new StringBuilder();

                foreach (var line in terminalLines)
                {
                    if (line != null)
                    {
                        sb.AppendLine(line.FormattedText);
                    }
                }

                return sb.ToString();
            }

            // Fallback to generic IEnumerable handling
            if (value is System.Collections.IEnumerable enumerable)
            {
                var sb = new StringBuilder();

                foreach (var item in enumerable)
                {
                    if (item is TerminalLine line && line != null)
                    {
                        sb.AppendLine(line.FormattedText);
                    }
                }

                return sb.ToString();
            }
        }
        catch (Exception ex)
        {
            // Log the error for debugging but don't crash the UI
            System.Diagnostics.Debug.WriteLine($"TerminalLinesToTextConverter error: {ex.Message}");
        }

        return string.Empty;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
