using System;
using System.Globalization;
using Avalonia.Controls;
using Avalonia.Data.Converters;
using TDA4_Emulator.Models;
using TDA4_Emulator.Views.TestModes;

namespace TDA4_Emulator.Converters;

/// <summary>
/// Converter that maps TestMode enum values to their corresponding UserControl views
/// </summary>
public class TestModeToViewConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is TestMode testMode)
        {
            return testMode switch
            {
                TestMode.IPC => new IpcTestModeView(),
                TestMode.TestMode1 => new TestMode1View(),
                TestMode.TestMode2 => new TestMode2View(),
                _ => new IpcTestModeView() // Default to IPC view
            };
        }
        
        return new IpcTestModeView(); // Default fallback
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
