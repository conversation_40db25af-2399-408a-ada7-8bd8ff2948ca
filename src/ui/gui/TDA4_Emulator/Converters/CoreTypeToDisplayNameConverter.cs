using System;
using System.Globalization;
using Avalonia.Data.Converters;
using TDA4_Emulator.Models;

namespace TDA4_Emulator.Converters;

/// <summary>
/// Converts CoreType enum values to their display names
/// </summary>
public class CoreTypeToDisplayNameConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is CoreType coreType)
        {
            return coreType.GetDisplayName();
        }
        
        return value?.ToString() ?? string.Empty;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
